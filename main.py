import pandas as pd


classes_df = pd.read_excel('classes.xlsx')
schedule_df = pd.read_excel('Lucknow_class_schedule.xlsx', sheet_name='Schedule')
batch_df = pd.read_excel('Lucknow_class_schedule.xlsx', sheet_name='Sheet1')
faculty_df = pd.read_excel('Lucknow_class_schedule.xlsx', sheet_name='Faculty Code')

print("\nBatch DataFrame Structure:")
print("Columns:", batch_df.columns.tolist())
print("\nFirst few rows of Batch DataFrame:")
print(batch_df.head())
print("\nShape of Batch DataFrame:", batch_df.shape)

print("\nfaculty_df DataFrame Structure:")
print("Columns:", faculty_df.columns.tolist())
print("\nFirst few rows of faculty_df:")
print(faculty_df.head())
print("\nShape of faculty_df:", faculty_df.shape)

# Print rows where FacultyCode is empty
empty_faculty_rows = schedule_df[schedule_df['FacultyCode'].isna()]
print("\nRows with empty FacultyCode:")
print(empty_faculty_rows)

# Convert LectDate to datetime and add day name column with standardized format
day_mapping = {
    'Mon': 'Mon',
    'Tue': 'Tue',
    'Wed': 'Wed',
    'Thu': 'Thu',
    'Fri': 'Fri',
    'Sat': 'Sat',
    'Sun': 'Sun'
}
schedule_df['Day'] = pd.to_datetime(schedule_df['LectDate']).dt.strftime('%a').map(day_mapping)

# Extract first three characters from Batch column
schedule_df['BatchCode'] = schedule_df['Batch'].str[:3]

# Extract hall number from Batch (last digit before H)
schedule_df['Hall'] = schedule_df['Batch'].str.extract(r'(\d)H')[0]

# Create a dictionary to store batch-wise subject schedules
batch_day_subjects = {}
excel_file = pd.ExcelFile('classes.xlsx')
for sheet in excel_file.sheet_names:
    df = pd.read_excel('classes.xlsx', sheet_name=sheet)
    # Store subjects for each day
    for day in df.columns:
        if day in day_mapping.values():  # Only process valid days
            subjects = df[day].iloc[0:2].tolist()
            if len(subjects) == 2:
                if sheet not in batch_day_subjects:
                    batch_day_subjects[sheet] = {}
                batch_day_subjects[sheet][day] = subjects

# Track which pattern each batch is using
batch_patterns = {}

# Track row count for each batch-date combination (changed from batch-day)
batch_date_counts = {}

# Function to get subject based on batch and day
def get_subject(row):
    batch_code = row['BatchCode']
    day = row['Day']
    date = row['LectDate']  # Get the actual date
    
    # Check if day is NaN
    if pd.isna(day):
        return None
        
    if batch_code in batch_day_subjects and day in batch_day_subjects[batch_code]:
        # Get the subjects for this batch and day
        subjects = batch_day_subjects[batch_code][day].copy()  # Make a copy to avoid modifying original
        
        # Initialize pattern for this batch if not exists
        if batch_code not in batch_patterns:
            # Get unique batch codes in order of appearance
            unique_batches = schedule_df['BatchCode'].unique()
            # Find index of current batch code
            batch_index = list(unique_batches).index(batch_code)
            # Assign pattern based on index
            batch_patterns[batch_code] = batch_index % 2
        
        # Get the pattern for this batch
        pattern = batch_patterns[batch_code]
        
        # Create key for batch-date combination (changed from batch-day)
        key = f"{batch_code}_{date}"
        
        # Initialize count for this batch-date if not exists
        if key not in batch_date_counts:
            batch_date_counts[key] = 0
        
        # Get current count and increment
        count = batch_date_counts[key]
        batch_date_counts[key] += 1
        
        # If pattern is 1, reverse the subjects list
        if pattern == 1:
            subjects.reverse()  # Reverse the list in place
        
        # Return first subject for even count, second for odd count
        return subjects[count % 2]
    
    return None

# Add subject column
schedule_df['Subject'] = schedule_df.apply(get_subject, axis=1)

# Function to get teacher name
def get_teacher(row):
    subject = row['Subject']
    batch = row['Batch']
    
    if pd.isna(subject) or pd.isna(batch):
        return None
        
    # Look up teacher in batch_df
    try:
        # Find the row where Subject/ Batch matches
        batch_row = batch_df[batch_df['Subject/ Batch'] == batch]
        if not batch_row.empty and subject in batch_df.columns:
            # Get the teacher name from the subject column
            teacher = batch_row[subject].iloc[0]
            return teacher
    except:
        return None
    return None

# Add teacher column
schedule_df['Teacher'] = schedule_df.apply(get_teacher, axis=1)

# Function to get faculty code
def get_faculty_code(row):
    teacher = row['Teacher']
    subject = row['Subject']
    
    if pd.isna(teacher) or pd.isna(subject):
        return None
        
    # Look up faculty code in faculty_df
    try:
        # Find the row where faculty_name and Expertise match
        faculty_row = faculty_df[
            (faculty_df['faculty_name'] == teacher) & 
            (faculty_df['Expertise'] == subject)
        ]
        if not faculty_row.empty:
            return faculty_row['FacultyCode'].iloc[0]
    except:
        return None
    return None

# Add faculty code column
schedule_df['FacultyCode'] = schedule_df.apply(get_faculty_code, axis=1)

print("\nFirst 10 rows of Schedule with all information:")
print(schedule_df[['LectDate', 'Batch', 'BatchCode', 'Hall', 'Day', 'Subject', 'Teacher', 'FacultyCode']].head(10))

schedule_df.to_excel('schedule_with_faculty_code.xlsx', index=False)


