import pandas as pd


classes_df = pd.read_excel('classes.xlsx')
schedule_df = pd.read_excel('Lucknow_class_schedule.xlsx', sheet_name='Schedule')
batch_df = pd.read_excel('Lucknow_class_schedule.xlsx', sheet_name='Sheet1')
faculty_df = pd.read_excel('Lucknow_class_schedule.xlsx', sheet_name='Faculty Code')

print("\nBatch DataFrame Structure:")
print("Columns:", batch_df.columns.tolist())
print("\nFirst few rows of Batch DataFrame:")
print(batch_df.head())
print("\nShape of Batch DataFrame:", batch_df.shape)

print("\nfaculty_df DataFrame Structure:")
print("Columns:", faculty_df.columns.tolist())
print("\nFirst few rows of faculty_df:")
print(faculty_df.head())
print("\nShape of faculty_df:", faculty_df.shape)

# Print rows where FacultyCode is empty
empty_faculty_rows = schedule_df[schedule_df['FacultyCode'].isna()]
print("\nRows with empty FacultyCode:")
print(empty_faculty_rows)

# Convert LectDate to datetime and add day name column with standardized format
day_mapping = {
    'Mon': 'Mon',
    'Tue': 'Tue',
    'Wed': 'Wed',
    'Thu': 'Thu',
    'Fri': 'Fri',
    'Sat': 'Sat',
    'Sun': 'Sun'
}
schedule_df['Day'] = pd.to_datetime(schedule_df['LectDate']).dt.strftime('%a').map(day_mapping)

# Extract first three characters from Batch column
schedule_df['BatchCode'] = schedule_df['Batch'].str[:3]

# Create a dictionary to store batch-wise subject schedules
batch_schedules = {}
excel_file = pd.ExcelFile('classes.xlsx')
for sheet in excel_file.sheet_names:
    df = pd.read_excel('classes.xlsx', sheet_name=sheet)
    # Store the schedule for this batch
    batch_schedules[sheet] = df

# Function to get subject based on batch and day
def get_subject(row):
    batch_code = row['BatchCode']
    day = row['Day']
    row_index = row.name  # Get the row index
    
    # Check if day is NaN
    if pd.isna(day):
        return None
        
    if batch_code in batch_schedules:
        schedule = batch_schedules[batch_code]
        # Check if the day exists in the schedule
        if day in schedule.columns:
            # Alternate between first and second subject based on row index
            subject_index = row_index % 2  # This will alternate between 0 and 1
            return schedule[day].iloc[subject_index]  # Get subject based on alternating index
    return None

# Add subject column
schedule_df['Subject'] = schedule_df.apply(get_subject, axis=1)

# Function to get teacher name
def get_teacher(row):
    subject = row['Subject']
    batch = row['Batch']
    
    if pd.isna(subject) or pd.isna(batch):
        return None
        
    # Look up teacher in batch_df
    try:
        # Find the row where Subject/ Batch matches
        batch_row = batch_df[batch_df['Subject/ Batch'] == batch]
        if not batch_row.empty and subject in batch_df.columns:
            # Get the teacher name from the subject column
            teacher = batch_row[subject].iloc[0]
            return teacher
    except:
        return None
    return None

# Add teacher column
schedule_df['Teacher'] = schedule_df.apply(get_teacher, axis=1)

# Function to get faculty code
def get_faculty_code(row):
    teacher = row['Teacher']
    subject = row['Subject']
    
    if pd.isna(teacher) or pd.isna(subject):
        return None
        
    # Look up faculty code in faculty_df
    try:
        # Find the row where faculty_name and Expertise match
        faculty_row = faculty_df[
            (faculty_df['faculty_name'] == teacher) & 
            (faculty_df['Expertise'] == subject)
        ]
        if not faculty_row.empty:
            return faculty_row['FacultyCode'].iloc[0]
    except:
        return None
    return None

# Add faculty code column
schedule_df['FacultyCode'] = schedule_df.apply(get_faculty_code, axis=1)

print("\nFirst 10 rows of Schedule with all information:")
print(schedule_df[['LectDate', 'Batch', 'BatchCode', 'Day', 'Subject', 'Teacher', 'FacultyCode']].head(10))


schedule_df.to_excel('schedule_with_faculty_code.xlsx', index=False)


